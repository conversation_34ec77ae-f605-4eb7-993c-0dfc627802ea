import React, { useState } from 'react';
import '../../styles/components/user-dashboard/SecuritySettings.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
// import lockIcon from '../../assets/lock.png';
// import keyIcon from '../../assets/key.png';

const SecuritySettings: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security-settings');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  const handleChangePassword = () => {
    // Navigate to change password page
    window.location.href = '/user-dashboard/change-password';
  };

  const handleResetPassword = () => {
    // Navigate to reset password page
    window.location.href = '/user-dashboard/reset-password';
  };

  return (
    <UserDashboardLayout className="security-settings-container">
      <div className="security-settings-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosín 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>
          </div>
          
          <div className="header-controls">
            <button className="stake-esvc-btn">
              <img src={lockIcon} alt="Stake" className="btn-icon" />
              Stake ESVC
            </button>
            
            <div className="balance-toggle">
              <span className="toggle-label">Hide balances</span>
              <label className="toggle-switch">
                <input 
                  type="checkbox" 
                  checked={hideBalances}
                  onChange={toggleBalances}
                />
                <span className="toggle-slider"></span>
              </label>
            </div>
          </div>
        </div>

        {/* Security Settings Content */}
        <div className="security-settings-main">
          <div className="settings-header">
            <h2 className="settings-title">Security Settings</h2>
          </div>

          <div className="security-options">
            <div className="security-option" onClick={handleChangePassword}>
              <div className="option-icon">
                <img src={lockIcon} alt="Change Password" className="security-icon" />
              </div>
              <div className="option-content">
                <h3 className="option-title">Change Password</h3>
                <p className="option-description">Update your password to keep your account secure.</p>
              </div>
              <div className="option-arrow">→</div>
            </div>

            <div className="security-option" onClick={handleResetPassword}>
              <div className="option-icon">
                <img src={keyIcon} alt="Reset Password" className="security-icon" />
              </div>
              <div className="option-content">
                <h3 className="option-title">Reset Password</h3>
                <p className="option-description">Reset your password if you've forgotten it or need to recover your account.</p>
              </div>
              <div className="option-arrow">→</div>
            </div>
          </div>
        </div>
      </div>

      {/* User Side Navigation */}
      <UserSideNav
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </UserDashboardLayout>
  );
};

export default SecuritySettings;
