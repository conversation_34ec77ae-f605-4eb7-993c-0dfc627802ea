/* Change Password Container */
.change-password-container {
  min-height: 100vh;
  background: #0A0A0A;
  position: relative;
}

.change-password-content {
  padding: 40px 60px 40px 320px;
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

/* User Header Background */
.user-header::before {
  content: '';
  position: absolute;
  top: -120px;
  left: -50vw;
  right: -50vw;
  height: 320px;
  background: #260D08;
  z-index: -1;
}

/* User Greeting Styles */
.greeting-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 48px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.greeting-subtitle {
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  color: #CCCCCC;
  margin: 0 0 32px 0;
  font-weight: 400;
}

/* Header Controls */
.stake-esvc-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.stake-esvc-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

.stake-esvc-btn .btn-icon {
  width: 20px;
  height: 20px;
}

/* Balance Toggle */
.balance-toggle {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  color: #CCCCCC;
  font-weight: 500;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #404040;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: #FFFFFF;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #BF4129;
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* User Header Layout */
.user-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 48px;
  position: relative;
  z-index: 1;
}

.user-greeting {
  flex: 1;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 24px;
  position: absolute;
  right: 0;
  top: 0;
}

/* Change Password Main */
.change-password-main {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid #333333;
  border-radius: 20px;
  padding: 32px;
  margin-top: 24px;
}

.password-header {
  margin-bottom: 32px;
}

.back-btn {
  background: none;
  border: none;
  color: #CCCCCC;
  font-family: 'Montserrat', sans-serif;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: color 0.3s ease;
}

.back-btn:hover {
  color: #FFFFFF;
}

/* Password Form */
.password-form-container {
  max-width: 500px;
}

.password-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
}

.form-input {
  padding: 16px 20px;
  background: #1A1A1A;
  border: 1px solid #404040;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 500;
  outline: none;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  border-color: #BF4129;
}

.form-input::placeholder {
  color: #666666;
}

/* Password Requirements */
.password-requirements {
  background: #1A1A1A;
  border: 1px solid #333333;
  border-radius: 12px;
  padding: 20px;
}

.requirements-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  color: #FFFFFF;
  margin: 0 0 12px 0;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.requirements-list li {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
}

.requirements-list li::before {
  content: '✓';
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.requirement-met {
  color: #10B981;
}

.requirement-met::before {
  background: #10B981;
  color: #FFFFFF;
}

.requirement-unmet {
  color: #6B7280;
}

.requirement-unmet::before {
  background: #374151;
  color: #6B7280;
}

/* Change Password Button */
.change-password-btn {
  width: 100%;
  padding: 16px 24px;
  background: #BF4129;
  border: none;
  border-radius: 12px;
  color: #FFFFFF;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 8px;
}

.change-password-btn:hover {
  background: #A63622;
  transform: translateY(-2px);
}

/* Success Modal */
.success-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.success-modal {
  background: #10B981;
  border-radius: 20px;
  padding: 40px;
  max-width: 400px;
  width: 100%;
  text-align: center;
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.success-icon {
  width: 80px;
  height: 80px;
  filter: brightness(0) invert(1);
}

.success-title {
  font-family: 'Montserrat', sans-serif;
  font-size: 24px;
  font-weight: 700;
  color: #FFFFFF;
  margin: 0;
}

.success-message {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  color: #FFFFFF;
  margin: 0;
  font-weight: 400;
  line-height: 1.5;
}

.success-btn {
  padding: 12px 32px;
  background: #FFFFFF;
  border: none;
  border-radius: 8px;
  color: #10B981;
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.success-btn:hover {
  background: #F3F4F6;
  transform: translateY(-2px);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .change-password-content {
    padding: 20px;
    margin-top: 0;
  }

  .user-header {
    flex-direction: column;
    gap: 24px;
    margin-bottom: 32px;
  }

  .user-greeting {
    width: 100%;
  }

  .greeting-text {
    font-size: 32px;
    margin-bottom: 8px;
  }

  .greeting-subtitle {
    margin-bottom: 24px;
  }

  .header-controls {
    position: static;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 0;
  }

  .change-password-main {
    padding: 24px;
    margin-top: 16px;
  }

  .back-btn {
    font-size: 16px;
  }

  .password-form-container {
    max-width: 100%;
  }

  .success-modal {
    margin: 10px;
    padding: 32px;
  }

  .success-icon {
    width: 60px;
    height: 60px;
  }

  .success-title {
    font-size: 20px;
  }

  .success-message {
    font-size: 14px;
  }
}
