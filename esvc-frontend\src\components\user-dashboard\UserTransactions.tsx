import React, { useState } from 'react';
import '../../styles/components/user-dashboard/UserTransactions.css';
import UserDashboardLayout from './UserDashboardLayout';

// Import icons
import esvcToken from '../../assets/esvc-token.png';
import usdcIcon from '../../assets/usdc.png';

interface UserTransactionsProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const UserTransactions: React.FC<UserTransactionsProps> = () => {
  const [activeTab, setActiveTab] = useState('stakes');
  const [timeFilter, setTimeFilter] = useState('all-time');

    const [showBalances, setShowBalances] = useState(true);
    const [hideBalances, setHideBalances] = useState(false);
  
    // Toggle balance visibility
    const toggleBalances = () => {
      setShowBalances(!showBalances);
      setHideBalances(!hideBalances);
    };
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  const timeFilters = [
    { id: 'all-time', label: 'All time' },
    { id: 'today', label: 'Today' },
    { id: 'week', label: 'This week' },
    { id: 'month', label: 'This month' }
  ];

  // Mock stakes data
  const stakesData = Array.from({ length: 50 }, (_, index) => ({
    id: index + 1,
    type: 'ESVC Staked',
    duration: '6 Months',
    from: 'From One...',
    amount: '900 ESVC',
    time: 'Today, 6:07 AM',
    icon: esvcToken
  }));

  // Mock withdrawals data
  const withdrawalsData = Array.from({ length: 30 }, (_, index) => ({
    id: index + 1,
    type: 'ROI Withdrawn',
    to: 'To 0x6d4...bF1',
    amount: '1,630 USDC',
    time: 'Today, 10:17 AM',
    icon: usdcIcon
  }));

  const currentData = activeTab === 'stakes' ? stakesData : withdrawalsData;
  const totalValue = '11,302';

  const totalPages = Math.ceil(currentData.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentTransactions = currentData.slice(startIndex, endIndex);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const renderPagination = () => {
    const centerPages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      centerPages.push(
        <button
          key={i}
          className={`pagination-btn ${currentPage === i ? 'active' : ''}`}
          onClick={() => handlePageChange(i)}
        >
          {i}
        </button>
      );
    }

    if (endPage < totalPages) {
      if (endPage < totalPages - 1) {
        centerPages.push(<span key="ellipsis" className="pagination-ellipsis">...</span>);
      }
      centerPages.push(
        <button
          key={totalPages}
          className={`pagination-btn ${currentPage === totalPages ? 'active' : ''}`}
          onClick={() => handlePageChange(totalPages)}
        >
          {totalPages}
        </button>
      );
    }

    return (
      <>
        <button
          className={`pagination-btn ${currentPage === 1 ? 'disabled' : ''}`}
          onClick={() => currentPage > 1 && handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ← Previous
        </button>

        <div className="pagination-center">
          {centerPages}
        </div>

        <button
          className={`pagination-btn ${currentPage === totalPages ? 'disabled' : ''}`}
          onClick={() => currentPage < totalPages && handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          Next →
        </button>
      </>
    );
  };

  return (
    <UserDashboardLayout>
      <div className="user-transactions-container">


       <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>


        {/* Wallet Selector */}
        <div className="wallet-selector">
          <select className="wallet-select">
            <option value="wallet1">🔒 Wallet 1 ($10,000 stakes)</option>
          </select>
        </div>

        {/* Transaction Tabs */}
        <div className="transaction-tabs">
          <button
            className={`transaction-tab ${activeTab === 'stakes' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('stakes');
              setCurrentPage(1);
            }}
          >
            Stakes
          </button>
          <button
            className={`transaction-tab ${activeTab === 'withdrawals' ? 'active' : ''}`}
            onClick={() => {
              setActiveTab('withdrawals');
              setCurrentPage(1);
            }}
          >
            Withdrawals
          </button>
        </div>

          <div className="dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

        {/* Filters and Total */}
        <div className="transactions-controls">
          <div className="time-filter">
            <select
              value={timeFilter}
              onChange={(e) => setTimeFilter(e.target.value)}
              className="filter-select"
            >
              {timeFilters.map((filter) => (
                <option key={filter.id} value={filter.id}>
                  {filter.label}
                </option>
              ))}
            </select>
          </div>

          <div className="total-value-card">
            <div className="total-label">TOTAL VALUE (BASED ON FILTER DATE)</div>
            <div className="total-amount">{totalValue}</div>
          </div>
        </div>

        {/* Transactions List */}
        <div className="transactions-list">
          {currentTransactions.map((transaction, index) => (
            <div key={transaction.id} className="transaction-item">
              <div className="transaction-number">{startIndex + index + 1}.</div>
              <div className="transaction-icon">
                <img src={transaction.icon} alt="Transaction icon" />
              </div>
              <div className="transaction-details">
                <div className="transaction-type">{transaction.type}</div>
                <div className="transaction-info">
                  {activeTab === 'stakes' ? (
                    <>
                      <span className="transaction-duration">{transaction.duration}</span>
                      <span className="transaction-from">{transaction.from}</span>
                    </>
                  ) : (
                    <span className="transaction-to">{transaction.to}</span>
                  )}
                </div>
              </div>
              <div className="transaction-amount-time">
                <div className="transaction-amount">{transaction.amount}</div>
                <div className="transaction-time">{transaction.time}</div>
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="pagination">
          {renderPagination()}
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default UserTransactions;
