import React, { useState } from 'react';
import '../../styles/components/user-dashboard/GetFunding.css';
import UserDashboardLayout from './UserDashboardLayout';
import UserSideNav from './UserSideNav';

// Import icons
import cardCoinIcon from '../../assets/card-coin.png';
import tickCircleIcon from '../../assets/tick-circle.png';
import exclamationIcon from '../../assets/Exclamation _Icon.png';

interface GetFundingProps {
  onNavigateToSignUp?: () => void;
  onNavigateToLogin?: () => void;
  onNavigateToLanding?: () => void;
}

const GetFunding: React.FC<GetFundingProps> = ({
  onNavigateToSignUp,
  onNavigateToLogin,
  onNavigateToLanding
}) => {
  const [activeTab, setActiveTab] = useState('get-funding');
  const [showBalances, setShowBalances] = useState(true);
  const [hideBalances, setHideBalances] = useState(false);
  const [eligibilityTab, setEligibilityTab] = useState('not-eligible'); // 'not-eligible' or 'eligible'
  const [showPitchForm, setShowPitchForm] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [formData, setFormData] = useState({
    problemStatement: '',
    solution: '',
    marketSize: '',
    ideaStage: '',
    fundingAmount: '',
    equityPercentage: '',
    location: '',
    country: '',
    contactInfo: ''
  });

  // Mock user data - in real app this would come from API
  const userStakeAmount = 700; // $700 staked
  const isEligible = userStakeAmount >= 500;

  // Toggle balance visibility
  const toggleBalances = () => {
    setShowBalances(!showBalances);
    setHideBalances(!hideBalances);
  };

  const handlePitchNow = () => {
    setShowPitchForm(true);
  };

  const handleGoBack = () => {
    setShowPitchForm(false);
  };

  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuccessModal(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
    setShowPitchForm(false);
    // Reset form
    setFormData({
      problemStatement: '',
      solution: '',
      marketSize: '',
      ideaStage: '',
      fundingAmount: '',
      equityPercentage: '',
      location: '',
      country: '',
      contactInfo: ''
    });
  };

  if (showPitchForm) {
    return (
      <UserDashboardLayout className="get-funding-container">
        <div className="get-funding-content">
          {/* User Greeting Header */}
          <div className="user-header">
            <div className="user-greeting">
              <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
              <p className="greeting-subtitle">Here is your staking overview</p>

              <div className="header-controls">
                <button className="stake-esvc-btn">
                  <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                  Stake ESVC
                </button>

                <div className="balance-toggle">
                  <span className="toggle-label">Show balances</span>
                  <label className="toggle-switch">
                    <input
                      type="checkbox"
                      checked={showBalances}
                      onChange={toggleBalances}
                    />
                    <span className="toggle-slider"></span>
                  </label>
                  <span className="toggle-label">Hide balances</span>
                </div>
              </div>
            </div>
          </div>

          <div className="dashboard-layout">
            {/* Sidebar */}
            <UserSideNav
              activeTab={activeTab}
              onTabChange={setActiveTab}
            />

            {/* Main Content */}
            <div className="funding-main-content">
              {/* Pitch Form */}
              <div className="pitch-form-container">
                <div className="pitch-form-header">
                  <button className="go-back-btn" onClick={handleGoBack}>
                    ← Pitch an Idea And Get Funded
                  </button>
                  <h2 className="form-title">Pitch Form</h2>
                </div>

                <form className="pitch-form" onSubmit={handleFormSubmit}>
                  <div className="form-group">
                    <label className="form-label">Problem Statement</label>
                    <p className="form-description">What problem are you solving?</p>
                    <textarea
                      name="problemStatement"
                      value={formData.problemStatement}
                      onChange={handleInputChange}
                      className="form-textarea"
                      rows={4}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Your Solution</label>
                    <p className="form-description">How does your idea or business solve this problem?</p>
                    <textarea
                      name="solution"
                      value={formData.solution}
                      onChange={handleInputChange}
                      className="form-textarea"
                      rows={4}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Market Size & Competition</label>
                    <p className="form-description">How big is the market, and who else is doing something similar?</p>
                    <textarea
                      name="marketSize"
                      value={formData.marketSize}
                      onChange={handleInputChange}
                      className="form-textarea"
                      rows={4}
                      required
                    />
                  </div>

                  <div className="form-group">
                    <label className="form-label">Stage of Your Idea</label>
                    <select
                      name="ideaStage"
                      value={formData.ideaStage}
                      onChange={handleInputChange}
                      className="form-select"
                      required
                    >
                      <option value="">Select stage...</option>
                      <option value="concept">Concept</option>
                      <option value="prototype">Prototype</option>
                      <option value="mvp">MVP</option>
                      <option value="early-revenue">Early Revenue</option>
                      <option value="scaling">Scaling</option>
                    </select>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">Amount You're Looking to Raise</label>
                      <p className="form-description">How much funding do you need?</p>
                      <input
                        type="text"
                        name="fundingAmount"
                        value={formData.fundingAmount}
                        onChange={handleInputChange}
                        className="form-input"
                        placeholder="$"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label className="form-label">Equity Willing to Give Out</label>
                      <p className="form-description">What percentage of equity are you willing to offer?</p>
                      <input
                        type="text"
                        name="equityPercentage"
                        value={formData.equityPercentage}
                        onChange={handleInputChange}
                        className="form-input"
                        placeholder="%"
                        required
                      />
                    </div>
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label className="form-label">Location</label>
                      <p className="form-description">Where are you based? Select your country and region.</p>
                      <input
                        type="text"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        className="form-input"
                        placeholder="Location..."
                        required
                      />
                    </div>

                    <div className="form-group">
                      <select
                        name="country"
                        value={formData.country}
                        onChange={handleInputChange}
                        className="form-select"
                        required
                      >
                        <option value="">Country...</option>
                        <option value="us">United States</option>
                        <option value="uk">United Kingdom</option>
                        <option value="ca">Canada</option>
                        <option value="ng">Nigeria</option>
                        <option value="other">Other</option>
                      </select>
                    </div>
                  </div>

                  <div className="form-group">
                    <label className="form-label">Contact Information</label>
                    <p className="form-description">How can we reach you?</p>
                    <input
                      type="email"
                      name="contactInfo"
                      value={formData.contactInfo}
                      onChange={handleInputChange}
                      className="form-input"
                      placeholder="Email address..."
                      required
                    />
                  </div>

                  <button type="submit" className="submit-btn">
                    Submit
                  </button>
                </form>
              </div>
            </div>
          </div>
        </div>

        {/* Success Modal */}
        {showSuccessModal && (
          <div className="modal-overlay">
            <div className="success-modal">
              <div className="success-icon">
                <img src={tickCircleIcon} alt="Success" />
              </div>
              <h3 className="success-title">Your pitch has been submitted successfully!</h3>
              <p className="success-message">
                Thank you for sharing your idea. Our team will review your 
                submission and contact you within a few days using the 
                details you provided.
              </p>
              <p className="success-note">
                In the meantime, you can continue staking and earning 
                rewards on our platform.
              </p>
              <button className="success-btn" onClick={closeSuccessModal}>
                Got it!
              </button>
            </div>
          </div>
        )}
      </UserDashboardLayout>
    );
  }

  return (
    <UserDashboardLayout className="get-funding-container">
      <div className="get-funding-content">
        {/* User Greeting Header */}
        <div className="user-header">
          <div className="user-greeting">
            <h1 className="greeting-text">Hi, Oluwatosin 👋</h1>
            <p className="greeting-subtitle">Here is your staking overview</p>

            <div className="header-controls">
              <button className="stake-esvc-btn">
                <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                Stake ESVC
              </button>

              <div className="balance-toggle">
                <span className="toggle-label">Show balances</span>
                <label className="toggle-switch">
                  <input
                    type="checkbox"
                    checked={showBalances}
                    onChange={toggleBalances}
                  />
                  <span className="toggle-slider"></span>
                </label>
                <span className="toggle-label">Hide balances</span>
              </div>
            </div>
          </div>
        </div>

        <div className="dashboard-layout">
          {/* Sidebar */}
          <UserSideNav
            activeTab={activeTab}
            onTabChange={setActiveTab}
          />

          {/* Main Content */}
          <div className="funding-main-content">
            {/* Eligibility Tabs */}
            <div className="eligibility-tabs">
              <button 
                className={`eligibility-tab ${eligibilityTab === 'not-eligible' ? 'active' : ''}`}
                onClick={() => setEligibilityTab('not-eligible')}
              >
                Not Eligible
              </button>
              <button 
                className={`eligibility-tab ${eligibilityTab === 'eligible' ? 'active' : ''}`}
                onClick={() => setEligibilityTab('eligible')}
              >
                Eligible
              </button>
            </div>

            {/* Funding Card */}
            <div className="funding-card">
              <h2 className="funding-title">Pitch an Idea And Get Funded</h2>
              
              {eligibilityTab === 'not-eligible' ? (
                <div className="not-eligible-content">
                  <div className="warning-message">
                    <img src={exclamationIcon} alt="Warning" className="warning-icon" />
                    <div className="warning-text">
                      <p>You're not eligible to pitch yet.</p>
                      <p>To submit your startup idea, you must stake at least:</p>
                      <ul>
                        <li><strong>$500</strong> worth of ESVC for online pitch sessions, or</li>
                        <li><strong>$500</strong> worth of ESVC to qualify for in-person pitch day events</li>
                      </ul>
                    </div>
                  </div>
                  <button className="stake-qualify-btn">
                    <img src={cardCoinIcon} alt="Stake" className="btn-icon" />
                    Stake Now to Qualify
                  </button>
                </div>
              ) : (
                <div className="eligible-content">
                  <div className="success-message">
                    <img src={tickCircleIcon} alt="Success" className="success-icon" />
                    <p>You've staked <strong>${userStakeAmount}</strong> in ESVC — you're eligible to pitch!</p>
                  </div>
                  <button className="pitch-now-btn" onClick={handlePitchNow}>
                    <img src={cardCoinIcon} alt="Pitch" className="btn-icon" />
                    Pitch Now
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </UserDashboardLayout>
  );
};

export default GetFunding;
